import React, { useState } from 'react';
import DentistSidebar from './DentistSidebar';
import Navbar from './Navbar';
import { FaUpload, FaSearch, FaCheckCircle, FaExclamationTriangle, FaMagic, FaRobot } from 'react-icons/fa';
import axios from 'axios';

const XRay = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [aiResult, setAiResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [enhancing, setEnhancing] = useState(false);
  const [error, setError] = useState('');
  const [annotatedImageUrl, setAnnotatedImageUrl] = useState(null);
  const [enhancedImageUrl, setEnhancedImageUrl] = useState(null);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setSelectedFile(file);
    setAiResult(null);
    setError('');
    setAnnotatedImageUrl(null);
    setEnhancedImageUrl(null);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setPreviewUrl(reader.result);
      reader.readAsDataURL(file);
    } else {
      setPreviewUrl(null);
    }
  };

  const handleAnalyze = async () => {
    if (!selectedFile) return;
    setLoading(true);
    setAiResult(null);
    setError('');
    setAnnotatedImageUrl(null);
    try {
      const formData = new FormData();
      formData.append('image', selectedFile);
      const response = await axios.post('/api/xray/analyze', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      if (response.data && response.data.results) {
        setAiResult({
          findings: response.data.results.map(r => ({ label: r.class, confidence: r.confidence })),
          summary: `AI detected: ${response.data.results.map(r => r.class).join(', ')}`
        });
        if (response.data.annotatedImagePath) {
          // Extract filename from the full path
          const filename = response.data.annotatedImagePath.split('/').pop();
          setAnnotatedImageUrl(`/api/xray/annotated-image/${filename}`);
        }
      } else {
        setError('No results from AI analysis.');
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to analyze X-ray image.');
    } finally {
      setLoading(false);
    }
  };

  const handleEnhance = async () => {
    if (!selectedFile) return;
    setEnhancing(true);
    setError('');
    setEnhancedImageUrl(null);
    try {
      const formData = new FormData();
      formData.append('image', selectedFile);
      const response = await axios.post('/api/xray/enhance', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      if (response.data && response.data.enhancedImage) {
        setEnhancedImageUrl(response.data.enhancedImage);
      } else {
        setError('No enhanced image received from server.');
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to enhance X-ray image.');
    } finally {
      setEnhancing(false);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <DentistSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold text-blue-900 mb-8">X-Ray Analysis</h1>
            <div className="bg-white rounded-xl shadow-md p-8 flex flex-col items-center gap-6 border border-blue-100">
              <label className="flex flex-col items-center cursor-pointer">
                <FaUpload className="text-blue-600 text-3xl mb-2" />
                <span className="text-blue-700 font-medium mb-2">Upload X-Ray Image</span>
                <input type="file" accept="image/*" className="hidden" onChange={handleFileChange} />
              </label>
              {previewUrl && (
                <img src={previewUrl} alt="X-Ray Preview" className="w-full max-w-xs rounded-lg border border-gray-200 shadow" />
              )}
              <div className="flex gap-4 mt-4">
                <button
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50"
                  onClick={handleEnhance}
                  disabled={!selectedFile || enhancing}
                >
                  <FaMagic /> {enhancing ? 'Enhancing...' : 'Enhance X-Ray'}
                </button>
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50"
                  onClick={handleAnalyze}
                  disabled={!selectedFile || loading}
                >
                  <FaRobot /> {loading ? 'Analyzing...' : 'AI Analysis'}
                </button>
              </div>
              {error && (
                <div className="w-full mt-4 bg-red-50 rounded-lg p-4 border border-red-200 text-red-700 flex items-center gap-2">
                  <FaExclamationTriangle /> {error}
                </div>
              )}
              {enhancedImageUrl && (
                <div className="w-full mt-6 bg-green-50 rounded-lg p-4 border border-green-200">
                  <h2 className="text-lg font-bold text-green-800 mb-2 flex items-center">
                    <FaMagic className="mr-2 text-green-500" />Enhanced X-Ray Image
                  </h2>
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                      <div className="font-semibold text-gray-700 mb-2">Original:</div>
                      <img src={previewUrl} alt="Original X-Ray" className="w-full max-w-sm rounded-lg border border-gray-300 shadow" />
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold text-green-700 mb-2">Enhanced:</div>
                      <img src={enhancedImageUrl} alt="Enhanced X-Ray" className="w-full max-w-sm rounded-lg border border-green-300 shadow" />
                    </div>
                  </div>
                  <div className="mt-3 text-green-900 font-medium">
                    ✅ X-ray image has been enhanced with improved contrast, reduced noise, and better visibility of dental structures.
                  </div>
                </div>
              )}
              {aiResult && (
                <div className="w-full mt-6 bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <h2 className="text-lg font-bold text-blue-800 mb-2 flex items-center">
                    <FaRobot className="mr-2 text-blue-500" />AI Analysis Results
                  </h2>
                  <div className="mb-4 p-3 bg-blue-100 rounded-lg">
                    <div className="text-sm text-blue-700 font-medium mb-2">
                      🤖 AI Model: YOLOv8 trained on dental X-ray dataset
                    </div>
                    <div className="text-xs text-blue-600">
                      Can detect: Caries, Crown, Filling, Implant, Malaligned teeth, Missing teeth,
                      Periapical lesions, Root canal treatment, Impacted teeth, Bone loss, Fractures,
                      Orthodontic brackets, and 18+ other dental conditions.
                    </div>
                  </div>
                  {aiResult.findings.length > 0 ? (
                    <>
                      <h3 className="font-semibold text-blue-800 mb-2">Detected Conditions:</h3>
                      <ul className="mb-3 space-y-1">
                        {aiResult.findings.map((f, idx) => (
                          <li key={idx} className="text-gray-700 flex items-center justify-between bg-white p-2 rounded border">
                            <span className="font-semibold text-blue-700">{f.label}</span>
                            <span className="text-sm bg-blue-100 px-2 py-1 rounded">
                              {(f.confidence * 100).toFixed(1)}% confidence
                            </span>
                          </li>
                        ))}
                      </ul>
                      <div className="text-blue-900 font-medium bg-blue-100 p-2 rounded">
                        📊 {aiResult.summary}
                      </div>
                    </>
                  ) : (
                    <div className="text-green-700 bg-green-100 p-3 rounded-lg">
                      ✅ No significant dental pathology detected in this X-ray image.
                    </div>
                  )}
                  {annotatedImageUrl && (
                    <div className="mt-4">
                      <div className="font-semibold text-blue-700 mb-2">Annotated X-Ray Image:</div>
                      <img src={annotatedImageUrl} alt="Annotated X-Ray" className="w-full max-w-md rounded-lg border border-blue-300 shadow" />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default XRay;