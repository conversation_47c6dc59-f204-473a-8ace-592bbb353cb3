const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// X-ray enhancement function using ai_enhance.py
const enhanceXrayImage = async (imagePath) => {
  return new Promise((resolve, reject) => {
    const outputPath = imagePath.replace(/\.[^/.]+$/, '_enhanced.png');
    const pythonProcess = spawn('python', ['ai_enhance.py', imagePath, outputPath], {
      cwd: path.join(__dirname, '..')
    });
    let output = '';
    let errorOutput = '';

    pythonProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    pythonProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    pythonProcess.on('close', (code) => {
      if (code === 0 && fs.existsSync(outputPath)) {
        console.log('✅ X-ray enhancement completed:', outputPath);
        resolve({
          enhancedImagePath: outputPath,
          originalImagePath: imagePath
        });
      } else {
        console.error('❌ Enhancement failed:', errorOutput);
        reject(new Error('Failed to enhance X-ray image: ' + errorOutput));
      }
    });
  });
};

// X-ray YOLOv8 analysis function using xray.pt model
const analyzeXrayWithYOLO = async (imagePath) => {
  return new Promise((resolve, reject) => {
    const pythonProcess = spawn('python', ['yolo_detect.py', 'xray.pt', imagePath], {
      cwd: path.join(__dirname, '..')
    });
    let output = '';
    let errorOutput = '';

    pythonProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    pythonProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    pythonProcess.on('close', (code) => {
      try {
        // Find the last JSON object in the output (in case there are progress messages)
        const lines = output.split('\n');
        let jsonOutput = '';
        for (let i = lines.length - 1; i >= 0; i--) {
          const line = lines[i].trim();
          if (line.startsWith('{') && line.endsWith('}')) {
            jsonOutput = line;
            break;
          }
        }
        
        if (!jsonOutput) {
          reject(new Error('No JSON output found from Python script'));
          return;
        }
        
        const result = JSON.parse(jsonOutput);
        if (result.error) {
          reject(new Error(result.error));
        } else {
          console.log('📊 X-ray YOLOv8 detections:', result.results);
          resolve({
            results: result.results,
            annotatedImagePath: result.annotated_image_path
          });
        }
      } catch (err) {
        console.error('❌ Python script output:', output);
        console.error('❌ Python script errors:', errorOutput);
        reject(new Error('Failed to parse YOLO output: ' + err.message));
      }
    });
  });
};

// API Routes
router.post('/analyze', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No X-ray image file provided' });
    }

    console.log('📸 Analyzing X-ray image:', req.file.filename);
    
    // Analyze X-ray image with YOLOv8 using xray.pt model
    const analysisResult = await analyzeXrayWithYOLO(req.file.path);
    
    // Clean up uploaded file
    fs.unlinkSync(req.file.path);
    
    console.log('✅ X-ray analysis results:', analysisResult.results);
    
    res.json({
      success: true,
      results: analysisResult.results,
      annotatedImagePath: analysisResult.annotatedImagePath,
      model: 'xray.pt',
      timestamp: new Date().toISOString(),
      imagePath: req.file.filename
    });
    
  } catch (error) {
    console.error('❌ Error analyzing X-ray image:', error);
    res.status(500).json({
      error: 'Failed to analyze X-ray image',
      details: error.message
    });
  }
});

// X-ray enhancement endpoint
router.post('/enhance', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No X-ray image file provided' });
    }

    console.log('🔧 Enhancing X-ray image:', req.file.filename);

    // Enhance X-ray image using ai_enhance.py
    const enhancementResult = await enhanceXrayImage(req.file.path);

    // Read the enhanced image and convert to base64 for response
    const enhancedImageBuffer = fs.readFileSync(enhancementResult.enhancedImagePath);
    const enhancedImageBase64 = enhancedImageBuffer.toString('base64');

    // Clean up uploaded and enhanced files
    fs.unlinkSync(req.file.path);
    fs.unlinkSync(enhancementResult.enhancedImagePath);

    console.log('✅ X-ray enhancement completed');

    res.json({
      success: true,
      enhancedImage: `data:image/png;base64,${enhancedImageBase64}`,
      message: 'X-ray image enhanced successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error enhancing X-ray image:', error);
    res.status(500).json({
      error: 'Failed to enhance X-ray image',
      details: error.message
    });
  }
});

// Serve annotated X-ray images
router.get('/annotated-image/:filename', (req, res) => {
  const filename = req.params.filename;
  // The annotated image is saved in the uploads directory
  const imagePath = path.join(__dirname, '..', 'uploads', filename);
  
  console.log('🔍 Looking for annotated X-ray image:', imagePath);
  
  if (fs.existsSync(imagePath)) {
    console.log('✅ Found annotated X-ray image, serving:', imagePath);
    // Set CORS headers for images
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET');
    res.header('Access-Control-Allow-Headers', 'Content-Type');
    res.sendFile(imagePath);
  } else {
    console.log('❌ Annotated X-ray image not found:', imagePath);
    res.status(404).json({ error: 'Annotated X-ray image not found' });
  }
});

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    model: 'xray.pt',
    modelPath: path.join(__dirname, '..', 'xray.pt'),
    modelExists: fs.existsSync(path.join(__dirname, '..', 'xray.pt')),
    classes: ['X-ray specific classes will be detected by the model'],
    timestamp: new Date().toISOString()
  });
});

module.exports = router; 